/**
 * Tests for the client script abstraction layer
 *
 * These tests verify that the abstraction layer correctly generates CDP Runtime.evaluate
 * expressions and handles results properly.
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { withCdp } from '../../../src/browser/client-api';
import type { CDPInstance } from '../../../src/browser/simple-cdp';

describe('Client Script Abstraction Layer', () => {
  let mockCdp: CDPInstance;
  let executionContextId: number;
  let sessionId: string;

  beforeEach(() => {
    executionContextId = 123;
    sessionId = 'test-session';

    // Mock CDP instance
    mockCdp = {
      Runtime: {
        evaluate: vi.fn(),
      },
    } as any;
  });

  describe('BrowserController', () => {
    it('should call init method correctly', async () => {
      const mockResult = {
        result: { value: undefined },
        exceptionDetails: undefined,
      };

      (mockCdp.Runtime.evaluate as any).mockResolvedValue(mockResult);

      const clientScripts = withCdp(mockCdp, executionContextId, sessionId);
      await clientScripts.BrowserController.init();

      expect(mockCdp.Runtime.evaluate).toHaveBeenCalledWith(
        {
          expression: expect.stringContaining('window.browserController.init()'),
          contextId: executionContextId,
          awaitPromise: true,
          returnByValue: true,
        },
        sessionId,
      );
    });

    it('should call takeScreenshot and return result', async () => {
      const mockScreenshotResult = {
        data: 'base64-screenshot-data',
        success: true,
      };

      const mockResult = {
        result: { value: mockScreenshotResult },
        exceptionDetails: undefined,
      };

      (mockCdp.Runtime.evaluate as any).mockResolvedValue(mockResult);

      const clientScripts = withCdp(mockCdp, executionContextId, sessionId);
      const result = await clientScripts.BrowserController.takeScreenshot();

      expect(result).toEqual(mockScreenshotResult);
      expect(mockCdp.Runtime.evaluate).toHaveBeenCalledWith(
        {
          expression: expect.stringContaining('window.browserController.takeScreenshot()'),
          contextId: executionContextId,
          awaitPromise: true,
          returnByValue: true,
        },
        sessionId,
      );
    });

    it('should handle mouse click with coordinates', async () => {
      const mockResult = {
        result: { value: undefined },
        exceptionDetails: undefined,
      };

      (mockCdp.Runtime.evaluate as any).mockResolvedValue(mockResult);

      const clientScripts = withCdp(mockCdp, executionContextId, sessionId);
      await clientScripts.BrowserController.dispatchMouseClick(100, 200);

      expect(mockCdp.Runtime.evaluate).toHaveBeenCalledWith(
        {
          expression: expect.stringContaining(
            'window.browserController.dispatchMouseClick(100, 200)',
          ),
          contextId: executionContextId,
          awaitPromise: true,
          returnByValue: true,
        },
        sessionId,
      );
    });
  });

  describe('ScreenCropper', () => {
    it('should call init with correct parameters', async () => {
      const mockResult = {
        result: { value: 'INITIALIZED' },
        exceptionDetails: undefined,
      };

      (mockCdp.Runtime.evaluate as any).mockResolvedValue(mockResult);

      const clientScripts = withCdp(mockCdp, executionContextId, sessionId);
      const result = await clientScripts.ScreenCropper.init('ws://localhost:8787');

      expect(result).toBe('INITIALIZED');
      expect(mockCdp.Runtime.evaluate).toHaveBeenCalledWith(
        {
          expression: expect.stringContaining('window.screenCropper.init'),
          contextId: executionContextId,
          awaitPromise: true,
          returnByValue: true,
        },
        sessionId,
      );
    });
  });

  describe('TensorFlowCaptchaDetector', () => {
    it('should call initialize with configuration', async () => {
      const mockResult = {
        result: { value: true },
        exceptionDetails: undefined,
      };

      (mockCdp.Runtime.evaluate as any).mockResolvedValue(mockResult);

      const clientScripts = withCdp(mockCdp, executionContextId, sessionId);
      const result = await clientScripts.TensorFlowCaptchaDetector.initialize({
        scoreThreshold: 0.5,
        debug: true,
      });

      expect(result).toBe(true);
      expect(mockCdp.Runtime.evaluate).toHaveBeenCalledWith(
        {
          expression: expect.stringContaining('window.tfCaptchaDetector.initialize'),
          contextId: executionContextId,
          awaitPromise: true,
          returnByValue: true,
        },
        sessionId,
      );
    });
  });

  describe('Error Handling', () => {
    it('should throw error when CDP execution fails', async () => {
      const mockResult = {
        result: { value: undefined },
        exceptionDetails: { text: 'Script execution failed' },
      };

      (mockCdp.Runtime.evaluate as any).mockResolvedValue(mockResult);

      const clientScripts = withCdp(mockCdp, executionContextId, sessionId);

      await expect(clientScripts.BrowserController.init()).rejects.toThrow(
        'Client script execution failed: Script execution failed',
      );
    });
  });

  describe('Type Safety', () => {
    it('should provide proper TypeScript types', () => {
      const clientScripts = withCdp(mockCdp, executionContextId, sessionId);

      // These should compile without TypeScript errors
      expect(typeof clientScripts.BrowserController.init).toBe('function');
      expect(typeof clientScripts.ScreenCropper.start).toBe('function');
      expect(typeof clientScripts.CaptchaDetector.initialize).toBe('function');
      expect(typeof clientScripts.TensorFlowCaptchaDetector.initialize).toBe('function');
    });
  });
});
